﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [Authorize]
    public class InspectionController : ControllerBase
    {
        private readonly IInspectionService _IInspectionService;
        private readonly ILogger<InspectionController> _logger;
        public InspectionController(IInspectionService inspectionService, IConfiguration configuration, ILogger<InspectionController> logger)
        {
            _logger = logger;
            _IInspectionService = inspectionService;
        }

        [HttpPost("CreateInspection")]
        public IActionResult CreateInspection([FromBody] CreateInspectionDto createInspectionDto)
        {
            var response = new ResponseDetails();
            try
            {
                long result = _IInspectionService.CreateInspection(createInspectionDto);
                response.Status = 1;
                response.Message = "Inspection created successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while creating inspection";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("VerifyActionParty")]
        public IActionResult VerifyActionParty([FromBody] VerifyActionPartyDto verifyActionPartyDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IInspectionService.VerifyActionParty(verifyActionPartyDto);
                response.Status = 1;
                response.Message = "Action party verified successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while verifying action party";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("VerifyInspector")]
        public IActionResult VerifyInspector([FromBody] VerifyInspectorDto verifyInspectorDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IInspectionService.VerifyInspector(verifyInspectorDto);
                response.Status = 1;
                response.Message = "Inspector verified successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while verifying inspector";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("GetInspections")]
        public IActionResult GetInspections()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IInspectionService.GetInspections();
                response.Status = 1;
                response.Message = "Inspections retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving inspections";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("GetActionParties")]
        public IActionResult GetActionParties()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IInspectionService.GetActionParties();
                response.Status = 1;
                response.Message = "Action parties retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving action parties";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}
