using Microsoft.AspNetCore.Http;

namespace Hsse.Application.IServices
{
    public interface IApiKeyService
    {
        /// <summary>
        /// Validates if the provided API key is valid
        /// </summary>
        /// <param name="api<PERSON><PERSON>">The API key to validate</param>
        /// <returns>True if the API key is valid, false otherwise</returns>
        bool IsValidApiKey(string apiKey);

        /// <summary>
        /// Extracts and validates the API key from the HTTP request headers
        /// </summary>
        /// <param name="context">The HTTP context containing the request</param>
        /// <returns>True if a valid API key is found in the headers, false otherwise</returns>
        bool IsValidApiKeyInRequest(HttpContext context);

        /// <summary>
        /// Gets the API key header name from configuration
        /// </summary>
        /// <returns>The header name for the API key</returns>
        string GetApiKeyHeaderName();
    }
}
