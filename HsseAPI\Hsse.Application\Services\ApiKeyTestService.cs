using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.IRepositories;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class ApiKeyTestService : IApiKeyTestService
    {
        private readonly IApiKeyTestRepository _apiKeyTestRepository;
        private readonly IApiKeyService _apiKeyService;
        private readonly ILogger<ApiKeyTestService> _logger;

        public ApiKeyTestService(
            IApiKeyTestRepository apiKeyTestRepository,
            IApiKeyService apiKeyService,
            ILogger<ApiKeyTestService> logger)
        {
            _apiKeyTestRepository = apiKeyTestRepository;
            _apiKeyService = apiKeyService;
            _logger = logger;
        }

        public ApiKeyTestResponseDto PerformApiKeyTest(HttpContext context)
        {
            try
            {
                _logger.LogInformation("Performing comprehensive API key test");

                var isValidKey = _apiKeyService.IsValidApiKeyInRequest(context);
                var headerName = _apiKeyService.GetApiKeyHeaderName();

                var response = new ApiKeyTestResponseDto
                {
                    Message = "API Key validation and encryption working correctly!",
                    Timestamp = DateTime.UtcNow,
                    HasValidApiKey = isValidKey,
                    ApiKeyHeader = headerName,
                    Status = isValidKey ? "SUCCESS" : "FAILED"
                };

                // Log the test activity through repository
                _apiKeyTestRepository.LogApiKeyTestActivity(context, isValidKey, "test");

                _logger.LogInformation($"API key test completed. Valid: {isValidKey}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing API key test");
                throw;
            }
        }

        public EchoTestResponseDto ProcessEchoTest(EchoTestRequestDto requestDto, HttpContext context)
        {
            try
            {
                _logger.LogInformation("Processing echo test through service layer");
                
                var result = _apiKeyTestRepository.ProcessEchoTest(requestDto, context);
                
                _logger.LogInformation("Echo test processed successfully through service layer");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing echo test in service layer");
                throw;
            }
        }

        public ApiKeyStatusResponseDto GetApiKeyStatus(HttpContext context)
        {
            try
            {
                _logger.LogInformation("Getting API key status through service layer");
                
                var result = _apiKeyTestRepository.GetApiKeyStatus(context);
                
                _logger.LogInformation($"API key status retrieved: {result.IsValidApiKey}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API key status in service layer");
                throw;
            }
        }

        public ApiKeyTestStatsDto GetTestStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                _logger.LogInformation($"Getting test statistics from {fromDate} to {toDate}");
                
                var result = _apiKeyTestRepository.GetApiKeyTestStats(fromDate, toDate);
                
                _logger.LogInformation($"Retrieved test statistics: {result.TotalTests} total tests");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting test statistics");
                throw;
            }
        }

        public ApiKeyValidationResultDto ValidateApiKeyWithDetails(HttpContext context)
        {
            try
            {
                _logger.LogInformation("Performing detailed API key validation");

                var headerName = _apiKeyService.GetApiKeyHeaderName();
                var headerPresent = context.Request.Headers.ContainsKey(headerName);
                var isValid = _apiKeyService.IsValidApiKeyInRequest(context);

                var result = new ApiKeyValidationResultDto
                {
                    IsValid = isValid,
                    HeaderName = headerName,
                    HeaderPresent = headerPresent,
                    ValidationMessage = GetValidationMessage(isValid, headerPresent),
                    ValidationTimestamp = DateTime.UtcNow,
                    RequestPath = context.Request.Path.ToString(),
                    IpAddress = context.Connection.RemoteIpAddress?.ToString() ?? "Unknown"
                };

                // Log the validation activity
                _apiKeyTestRepository.LogApiKeyTestActivity(context, isValid, "validation");

                _logger.LogInformation($"Detailed validation completed. Valid: {isValid}, Header Present: {headerPresent}");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing detailed API key validation");
                throw;
            }
        }

        private string GetValidationMessage(bool isValid, bool headerPresent)
        {
            if (!headerPresent)
                return "API key header is missing from the request";
            
            if (!isValid)
                return "API key is present but invalid";
            
            return "API key is valid and authorized";
        }
    }
}
