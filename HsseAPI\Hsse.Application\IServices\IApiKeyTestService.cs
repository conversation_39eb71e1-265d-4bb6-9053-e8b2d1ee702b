using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.IRepositories;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface IApiKeyTestService
    {
        /// <summary>
        /// Performs comprehensive API key and encryption test
        /// </summary>
        /// <param name="context">HTTP context for the request</param>
        /// <returns>Test results</returns>
        ApiKeyTestResponseDto PerformApiKeyTest(HttpContext context);

        /// <summary>
        /// Processes echo test with encryption validation
        /// </summary>
        /// <param name="requestDto">Echo test request data</param>
        /// <param name="context">HTTP context</param>
        /// <returns>Echo test response</returns>
        EchoTestResponseDto ProcessEchoTest(EchoTestRequestDto requestDto, HttpContext context);

        /// <summary>
        /// Gets detailed API key validation status
        /// </summary>
        /// <param name="context">HTTP context</param>
        /// <returns>API key status information</returns>
        ApiKeyStatusResponseDto GetApiKeyStatus(HttpContext context);

        /// <summary>
        /// Gets API key test statistics and analytics
        /// </summary>
        /// <param name="fromDate">Start date for statistics</param>
        /// <param name="toDate">End date for statistics</param>
        /// <returns>Test statistics</returns>
        ApiKeyTestStatsDto GetTestStatistics(DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Validates API key and returns detailed validation information
        /// </summary>
        /// <param name="context">HTTP context</param>
        /// <returns>Validation details</returns>
        ApiKeyValidationResultDto ValidateApiKeyWithDetails(HttpContext context);
    }

    public class ApiKeyValidationResultDto
    {
        public bool IsValid { get; set; }
        public string HeaderName { get; set; } = string.Empty;
        public bool HeaderPresent { get; set; }
        public string ValidationMessage { get; set; } = string.Empty;
        public DateTime ValidationTimestamp { get; set; } = DateTime.UtcNow;
        public string RequestPath { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
    }
}
