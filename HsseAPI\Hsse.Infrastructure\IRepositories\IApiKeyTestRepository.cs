using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IApiKeyTestRepository
    {
        /// <summary>
        /// Logs API key test activity to database
        /// </summary>
        /// <param name="context">HTTP context for request details</param>
        /// <param name="isValidKey">Whether the API key was valid</param>
        /// <param name="testType">Type of test performed</param>
        /// <returns>Log entry ID</returns>
        long LogApiKeyTestActivity(HttpContext context, bool isValidKey, string testType);

        /// <summary>
        /// Gets API key test statistics
        /// </summary>
        /// <param name="fromDate">Start date for statistics</param>
        /// <param name="toDate">End date for statistics</param>
        /// <returns>Test statistics</returns>
        ApiKeyTestStatsDto GetApiKeyTestStats(DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Validates and processes echo test data
        /// </summary>
        /// <param name="requestDto">Echo test request data</param>
        /// <param name="context">HTTP context</param>
        /// <returns>Processed echo response</returns>
        EchoTestResponseDto ProcessEchoTest(EchoTestRequestDto requestDto, HttpContext context);

        /// <summary>
        /// Gets API key validation status with detailed information
        /// </summary>
        /// <param name="context">HTTP context</param>
        /// <returns>API key status details</returns>
        ApiKeyStatusResponseDto GetApiKeyStatus(HttpContext context);
    }

    public class ApiKeyTestStatsDto
    {
        public int TotalTests { get; set; }
        public int ValidKeyTests { get; set; }
        public int InvalidKeyTests { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public Dictionary<string, int> TestTypeBreakdown { get; set; } = new();
    }
}
