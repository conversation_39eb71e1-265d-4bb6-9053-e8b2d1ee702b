﻿using Hsse.Infrastructure.IRepositories;
using Hsse.Infrastructure.Repositories;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructureDI(this IServiceCollection services)
        {
            services.AddScoped<IPostRepository, PostRepository>();
            services.AddScoped<IAnnouncementRepository, AnnouncementRepository>();
            services.AddScoped<IInspectionRepository, InspectionRepository>();
            services.AddScoped<IEventRepository, EventRepository>();
            services.AddScoped<IFeedbackRepository, FeedbackRepository>();
            services.AddScoped<IDocumentRepository, DocumentRepository>();
            services.AddScoped<ILoginRepository, LoginRepository>();
            services.AddScoped<IOtpRepository, OtpRepository>();
            services.AddScoped<IApiKeyTestRepository, ApiKeyTestRepository>();
            return services;
        }
    }
}
