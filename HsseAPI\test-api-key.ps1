# PowerShell script to test API Key implementation
# Make sure the API is running before executing this script

$baseUrl = "http://localhost:5000"
$validApiKey = "hsse-api-key-2024-secure"
$invalidApiKey = "invalid-key"

Write-Host "Testing HSSE API X-API-Key Implementation" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Test 1: Request without API Key (should fail)
Write-Host "`nTest 1: Request without API Key" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/ApiKeyTest/status" -Method GET -ErrorAction Stop
    Write-Host "UNEXPECTED: Request succeeded without API key" -ForegroundColor Red
    Write-Host $response
} catch {
    Write-Host "EXPECTED: Request failed without API key" -ForegroundColor Green
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Cyan
}

# Test 2: Request with invalid API Key (should fail)
Write-Host "`nTest 2: Request with invalid API Key" -ForegroundColor Yellow
try {
    $headers = @{ "X-API-Key" = $invalidApiKey }
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/ApiKeyTest/status" -Method GET -Headers $headers -ErrorAction Stop
    Write-Host "UNEXPECTED: Request succeeded with invalid API key" -ForegroundColor Red
    Write-Host $response
} catch {
    Write-Host "EXPECTED: Request failed with invalid API key" -ForegroundColor Green
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Cyan
}

# Test 3: Request with valid API Key (should succeed)
Write-Host "`nTest 3: Request with valid API Key" -ForegroundColor Yellow
try {
    $headers = @{ "X-API-Key" = $validApiKey }
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/ApiKeyTest/status" -Method GET -Headers $headers -ErrorAction Stop
    Write-Host "SUCCESS: Request succeeded with valid API key" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "UNEXPECTED: Request failed with valid API key" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test encryption endpoint with valid API Key
Write-Host "`nTest 4: Test encryption endpoint with valid API Key" -ForegroundColor Yellow
try {
    $headers = @{ "X-API-Key" = $validApiKey }
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/ApiKeyTest/test" -Method GET -Headers $headers -ErrorAction Stop
    Write-Host "SUCCESS: Encryption test succeeded with valid API key" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "ERROR: Encryption test failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test echo endpoint with valid API Key
Write-Host "`nTest 5: Test echo endpoint with valid API Key" -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = $validApiKey
        "Content-Type" = "application/json"
    }
    $body = @{
        Message = "Hello from PowerShell test"
        Data = @{
            TestProperty = "Test Value"
            Number = 42
        }
        AdditionalProperties = @{
            Source = "PowerShell Script"
            Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        }
    } | ConvertTo-Json -Depth 3

    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/ApiKeyTest/echo" -Method POST -Headers $headers -Body $body -ErrorAction Stop
    Write-Host "SUCCESS: Echo test succeeded with valid API key" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "ERROR: Echo test failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Test detailed validation endpoint
Write-Host "`nTest 6: Test detailed validation endpoint" -ForegroundColor Yellow
try {
    $headers = @{ "X-API-Key" = $validApiKey }
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/ApiKeyTest/validation" -Method GET -Headers $headers -ErrorAction Stop
    Write-Host "SUCCESS: Detailed validation succeeded" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "ERROR: Detailed validation failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Test statistics endpoint
Write-Host "`nTest 7: Test statistics endpoint" -ForegroundColor Yellow
try {
    $headers = @{ "X-API-Key" = $validApiKey }
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/ApiKeyTest/statistics" -Method GET -Headers $headers -ErrorAction Stop
    Write-Host "SUCCESS: Statistics retrieval succeeded" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "ERROR: Statistics retrieval failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTesting completed!" -ForegroundColor Green
Write-Host "Make sure to start the API with: dotnet run --project Hsse.API" -ForegroundColor Cyan
