﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [Authorize]
    public class PostController : ControllerBase
    {
        private readonly IPostService _IPostService;
        private readonly ILogger<PostController> _logger;
        public PostController(IPostService postService, IConfiguration configuration, ILogger<PostController> logger)
        {
            _logger = logger;
            _IPostService = postService;
        }
        [HttpGet("GetPostsCountsByUserId")]
        public IActionResult GetPostsCountsByUserId(int userId)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetPostsCountsByUserId(userId);
                response.Status = 1;
                response.Message = "Posts counts retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving posts";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpPost("CreatePost")]
        public IActionResult CreatePost([FromBody] CreatePostDto createPostDto)
        {
            var response = new ResponseDetails();
            try
            {
                if (!string.IsNullOrEmpty(createPostDto.ImageBase64) && !string.IsNullOrEmpty(createPostDto.FileName))
                {
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "post-files");

                    if (!Directory.Exists(uploadFolder))
                        Directory.CreateDirectory(uploadFolder);

                    string uniqueFileName = $"{Guid.NewGuid()}_{createPostDto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    string base64Data = createPostDto.ImageBase64.Contains(",")
                        ? createPostDto.ImageBase64.Substring(createPostDto.ImageBase64.IndexOf(",") + 1)
                        : createPostDto.ImageBase64;

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                    // Save virtual path or DB path if needed
                    createPostDto.ImageBase64 = $"/ExternalFiles/post-files/{uniqueFileName}";
                }
                long result = _IPostService.CreatePost(createPostDto);
                if (result == 1)
                {
                    response.Status = 1;
                    response.Message = "Post created successfully";
                    response.Result = result;
                }
                else
                {
                    response.Status = 2;
                    response.Message = "Post updated successfully";
                    response.Result = null;
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while creating post";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpGet("GetPosts")]
        public IActionResult GetPosts([FromQuery] PaginationDto pagination, int? userId = null)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetPosts(pagination, userId);
                response.Status = 1;
                response.Message = "Posts retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving posts";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpGet("GetPostCategories")]
        public IActionResult GetPostCategories()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetPostCategories();
                response.Status = 1;
                response.Message = "Post categories retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving post categories";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpPost("CreateOrUpdateLikes")]
        public IActionResult CreateOrUpdateLikes([FromBody] CreateLikeDto createLikeDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.CreateOrUpdateLikes(createLikeDto);
                if (result == 1)
                {
                    response.Status = 1;
                    response.Message = "Like added successfully";
                    response.Result = result;
                }
                else
                {
                    response.Status = 2;
                    response.Message = "Like updated successfully";
                    response.Result = result;
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing like operation";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPut("ClosePost")]
        public IActionResult ClosePost(ClosedPostDto closedPostDto)
        {
            var response = new ResponseDetails();
            try
            {
                if (!string.IsNullOrEmpty(closedPostDto.ImageBase64) && !string.IsNullOrEmpty(closedPostDto.FileName))
                {
                    string externalRootPath = @"C:\HSSE-Announcements";
                    string uploadFolder = Path.Combine(externalRootPath, "post-files");

                    if (!Directory.Exists(uploadFolder))
                        Directory.CreateDirectory(uploadFolder);

                    string uniqueFileName = $"{Guid.NewGuid()}_{closedPostDto.FileName}";
                    string filePath = Path.Combine(uploadFolder, uniqueFileName);

                    string base64Data = closedPostDto.ImageBase64.Contains(",")
                        ? closedPostDto.ImageBase64.Substring(closedPostDto.ImageBase64.IndexOf(",") + 1)
                        : closedPostDto.ImageBase64;

                    byte[] fileBytes = Convert.FromBase64String(base64Data);
                    System.IO.File.WriteAllBytesAsync(filePath, fileBytes);

                    // Save virtual path or DB path if needed
                    closedPostDto.ImageBase64 = $"/ExternalFiles/post-files/{uniqueFileName}";
                }
                int result = _IPostService.ClosePost(closedPostDto);
                if (result == 1)
                {
                    response.Status = 1;
                    response.Message = "Post closed successfully";
                    response.Result = result;
                }
                else
                {
                    response.Status = 0;
                    response.Message = "Post not found";
                    response.Result = null;
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while closing post";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpDelete("DeletePost")]
        public IActionResult DeletePost(int postId, int deletedBy)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.DeletePost(postId, deletedBy);
                if (result == 1)
                {
                    response.Status = 1;
                    response.Message = "Post deleted successfully";
                    response.Result = result;
                }
                else
                {
                    response.Status = 0;
                    response.Message = "Post not found";
                    response.Result = null;
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while deleting post";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("CreateOrUpdateComment")]
        public IActionResult CreateOrUpdateComment([FromBody] CreateCommentDto createCommentDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.CreateOrUpdateComment(createCommentDto);
                response.Status = 1;
                response.Message = "Comment operation completed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing comment operation";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpPost("CreateOrUpdateCommentLikes")]
        public IActionResult CreateOrUpdateCommentLikes([FromBody] CommentLikeCreateDto commentLikeDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.CreateOrUpdateCommentLikes(commentLikeDto);
                if (result == 1)
                {
                    response.Status = 1;
                    response.Message = "Like added successfully";
                    response.Result = result;
                }
                else
                {
                    response.Status = 2;
                    response.Message = "Like updated successfully";
                    response.Result = result;
                }
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing like operation";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpGet("GetCommentsByPostId")]
        public IActionResult GetCommentsByPostId(int PostId)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetCommentsByPostId(PostId);
                response.Status = 1;
                response.Message = "Post comments retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving post categories";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpGet("GetLikesByPostId")]
        public IActionResult GetLikesByPostId(int PostId)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetLikesByPostId(PostId);
                response.Status = 1;
                response.Message = "Post likes retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving post categories";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}
