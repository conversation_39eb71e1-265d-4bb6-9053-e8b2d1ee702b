# X-API-Key Implementation for HSSE API

## Overview
The HSSE API now requires a valid X-API-Key header for all requests. The encryption middleware will only encrypt/decrypt requests and responses when a valid API key is provided.

## Configuration

### API Keys
Valid API keys are configured in `appsettings.json`:

```json
{
  "ApiKeySettings": {
    "ValidApiKeys": [
      "hsse-api-key-2024-secure",
      "mobile-app-key-v1-2024", 
      "admin-access-key-2024"
    ],
    "HeaderName": "X-API-Key"
  }
}
```

### Adding New API Keys
To add new API keys, simply add them to the `ValidApiKeys` array in the configuration file.

## Usage

### Required Header
All API requests must include the X-API-Key header:

```
X-API-Key: hsse-api-key-2024-secure
```

### Request Flow
1. **API Key Validation**: The middleware first checks for a valid X-API-Key header
2. **Encryption/Decryption**: If the API key is valid, the middleware proceeds with encryption/decryption
3. **Unauthorized Response**: If the API key is invalid or missing, returns 401 Unauthorized

### Example Requests

#### Valid Request with API Key
```bash
curl -X GET "https://localhost:7000/api/v1/ApiKeyTest/test" \
  -H "X-API-Key: hsse-api-key-2024-secure" \
  -H "Authorization: Bearer your-jwt-token"
```

#### Invalid Request (Missing API Key)
```bash
curl -X GET "https://localhost:7000/api/v1/ApiKeyTest/test"
# Returns: 401 Unauthorized: Invalid or missing API key
```

## Test Endpoints

### GET /api/v1/ApiKeyTest/test
Tests API key validation and encryption functionality through the service layer.

### POST /api/v1/ApiKeyTest/echo
Tests encrypted request/response with API key validation. Accepts `EchoTestRequestDto` with message, data, and additional properties.

### GET /api/v1/ApiKeyTest/status
Returns API key validation status through the service and repository layers.

### GET /api/v1/ApiKeyTest/validation
Returns detailed API key validation information including header presence, validation timestamp, and request details.

### GET /api/v1/ApiKeyTest/statistics
Returns API key test statistics and analytics (mock data for demonstration).

## Swagger Documentation
The Swagger UI now includes both Bearer token and X-API-Key authentication options:
- **Bearer**: For JWT token authentication
- **ApiKey**: For X-API-Key header authentication

## Security Features

### API Key Validation
- Validates API key presence in request headers
- Checks against configured list of valid keys
- Logs validation attempts for security monitoring

### Encryption Protection
- Encryption/decryption only occurs with valid API key
- Prevents unauthorized access to encrypted endpoints
- Maintains existing JWT authentication for authorized endpoints

## Error Responses

### 401 Unauthorized
Returned when:
- X-API-Key header is missing
- X-API-Key header is empty
- X-API-Key value is not in the valid keys list

Response body: `"Unauthorized: Invalid or missing API key"`

## Implementation Details

### New Components

#### Service Layer
- `IApiKeyService`: Interface for API key validation
- `ApiKeyService`: Implementation of API key validation logic
- `IApiKeyTestService`: Interface for API key test functionality
- `ApiKeyTestService`: Service layer implementation for test operations

#### Repository Layer
- `IApiKeyTestRepository`: Interface for API key test data operations
- `ApiKeyTestRepository`: Repository implementation with logging and validation

#### DTOs
- `ApiKeyTestRequestDto`: Request DTO for API key tests
- `EchoTestRequestDto`: Request DTO for echo tests
- `ApiKeyTestResponseDto`: Response DTO for test results
- `EchoTestResponseDto`: Response DTO for echo tests
- `ApiKeyStatusResponseDto`: Response DTO for status checks
- `ApiKeyValidationResultDto`: Detailed validation result DTO

#### Middleware
- Updated `EncryptionMiddleware`: Includes API key validation before encryption

#### Controllers
- `ApiKeyTestController`: Test endpoints using proper service/repository pattern

### Dependency Injection
All services and repositories are registered as scoped services in the DI container following the existing pattern.

### Logging
API key validation attempts are logged for security monitoring and debugging.
