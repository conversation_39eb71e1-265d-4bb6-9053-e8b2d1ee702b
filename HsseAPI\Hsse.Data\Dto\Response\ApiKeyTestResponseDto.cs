using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class ApiKeyTestResponseDto
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool HasValidApiKey { get; set; }
        public string ApiKeyHeader { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    public class EchoTestResponseDto
    {
        public string Message { get; set; } = string.Empty;
        public object? ReceivedData { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool HasValidApiKey { get; set; }
        public string ProcessingInfo { get; set; } = string.Empty;
    }

    public class ApiKeyStatusResponseDto
    {
        public bool IsValidApiKey { get; set; }
        public string HeaderName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string Message { get; set; } = string.Empty;
        public string ValidationDetails { get; set; } = string.Empty;
    }
}
