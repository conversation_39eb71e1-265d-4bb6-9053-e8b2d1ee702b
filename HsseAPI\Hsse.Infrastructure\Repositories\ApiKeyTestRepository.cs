using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class ApiKeyTestRepository : IApiKeyTestRepository
    {
        private readonly MasterDBContext _masterDBContext;
        private readonly ILogger<ApiKeyTestRepository> _logger;
        private readonly IConfiguration _configuration;
        private readonly List<string> _validApiKeys;
        private readonly string _headerName;

        public ApiKeyTestRepository(
            MasterDBContext masterDBContext,
            ILogger<ApiKeyTestRepository> logger,
            IConfiguration configuration)
        {
            _masterDBContext = masterDBContext;
            _logger = logger;
            _configuration = configuration;

            // Load API key settings
            var apiKeySettings = _configuration.GetSection("ApiKeySettings");
            _validApiKeys = apiKeySettings.GetSection("ValidApiKeys").Get<List<string>>() ?? new List<string>();
            _headerName = apiKeySettings["HeaderName"] ?? "X-API-Key";
        }

        public long LogApiKeyTestActivity(HttpContext context, bool isValidKey, string testType)
        {
            try
            {
                _logger.LogInformation($"Logging API key test activity: {testType}, Valid: {isValidKey}");

                // For now, we'll just log to the application logs since we don't have a specific table
                // In a real implementation, you might want to create a dedicated audit table
                var logEntry = new
                {
                    Timestamp = DateTime.UtcNow,
                    TestType = testType,
                    IsValidKey = isValidKey,
                    IpAddress = context.Connection.RemoteIpAddress?.ToString(),
                    UserAgent = context.Request.Headers["User-Agent"].ToString(),
                    RequestPath = context.Request.Path.ToString()
                };

                _logger.LogInformation($"API Key Test Activity: {JsonSerializer.Serialize(logEntry)}");

                // Return a mock ID for now
                return DateTime.UtcNow.Ticks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging API key test activity");
                throw;
            }
        }

        public ApiKeyTestStatsDto GetApiKeyTestStats(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                // Since we don't have a dedicated table, return mock statistics
                // In a real implementation, you would query your audit table
                var stats = new ApiKeyTestStatsDto
                {
                    FromDate = fromDate ?? DateTime.UtcNow.AddDays(-30),
                    ToDate = toDate ?? DateTime.UtcNow,
                    TotalTests = 100, // Mock data
                    ValidKeyTests = 85,
                    InvalidKeyTests = 15,
                    TestTypeBreakdown = new Dictionary<string, int>
                    {
                        { "status", 40 },
                        { "test", 35 },
                        { "echo", 25 }
                    }
                };

                _logger.LogInformation($"Retrieved API key test stats: {JsonSerializer.Serialize(stats)}");
                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving API key test stats");
                throw;
            }
        }

        public EchoTestResponseDto ProcessEchoTest(EchoTestRequestDto requestDto, HttpContext context)
        {
            try
            {
                var isValidKey = IsValidApiKeyInRequest(context);
                
                var response = new EchoTestResponseDto
                {
                    Message = "Echo test processed successfully",
                    ReceivedData = requestDto,
                    Timestamp = DateTime.UtcNow,
                    HasValidApiKey = isValidKey,
                    ProcessingInfo = $"Request processed at {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC"
                };

                // Log the test activity
                LogApiKeyTestActivity(context, isValidKey, "echo");

                _logger.LogInformation($"Processed echo test for valid key: {isValidKey}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing echo test");
                throw;
            }
        }

        public ApiKeyStatusResponseDto GetApiKeyStatus(HttpContext context)
        {
            try
            {
                var isValidKey = IsValidApiKeyInRequest(context);
                var headerName = _headerName;

                var response = new ApiKeyStatusResponseDto
                {
                    IsValidApiKey = isValidKey,
                    HeaderName = headerName,
                    Timestamp = DateTime.UtcNow,
                    Message = isValidKey ? "Valid API key provided" : "Invalid or missing API key",
                    ValidationDetails = $"Validation performed at {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC"
                };

                // Log the test activity
                LogApiKeyTestActivity(context, isValidKey, "status");

                _logger.LogInformation($"API key status check: Valid={isValidKey}");
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting API key status");
                throw;
            }
        }

        private bool IsValidApiKeyInRequest(HttpContext context)
        {
            if (context?.Request?.Headers == null)
            {
                return false;
            }

            if (!context.Request.Headers.TryGetValue(_headerName, out var apiKeyValues))
            {
                return false;
            }

            var apiKey = apiKeyValues.FirstOrDefault();
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                return false;
            }

            return _validApiKeys.Contains(apiKey);
        }
    }
}
