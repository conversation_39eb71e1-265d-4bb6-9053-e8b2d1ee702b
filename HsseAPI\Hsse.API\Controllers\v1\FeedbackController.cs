using Asp.Versioning;
using Hsse.Application.Helper;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [Authorize]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackService _IFeedbackService;
        private readonly ILogger<FeedbackController> _logger;
        private readonly EncryptionHelper _encryptionHelper;

        public FeedbackController(IFeedbackService feedbackService, EncryptionHelper encryptionHelper, IConfiguration configuration, ILogger<FeedbackController> logger)
        {
            _logger = logger;
            _IFeedbackService = feedbackService;
            _encryptionHelper = encryptionHelper;
        }
        [HttpPost("echo")]
        public IActionResult Echo()
        {
            using var reader = new StreamReader(Request.Body);
            var encryptedBody = reader.ReadToEnd();
            var decryptedBody = _encryptionHelper.Decrypt(encryptedBody);

            // Do something with decryptedBody (e.g., deserialize it)
            var response = decryptedBody;

            var encryptedResponse = _encryptionHelper.Encrypt(response);
            return Ok(encryptedResponse);
        }

        [HttpGet("encrypt")]
        public IActionResult encrypt()
        {
           var str = "Hello, AES!";
            var encryptedResponse = _encryptionHelper.Encrypt(str);
            return Ok(encryptedResponse);
        }



    }
}
