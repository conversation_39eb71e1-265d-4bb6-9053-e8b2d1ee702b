using Hsse.Application.IServices;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Hsse.Application.Services
{
    public class ApiKeyService : IApiKeyService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ApiKeyService> _logger;
        private readonly List<string> _validApiKeys;
        private readonly string _headerName;

        public ApiKeyService(IConfiguration configuration, ILogger<ApiKeyService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            // Load valid API keys from configuration
            var apiKeySettings = _configuration.GetSection("ApiKeySettings");
            _validApiKeys = apiKeySettings.GetSection("ValidApiKeys").Get<List<string>>() ?? new List<string>();
            _headerName = apiKeySettings["HeaderName"] ?? "X-API-Key";

            _logger.LogInformation($"ApiKeyService initialized with {_validApiKeys.Count} valid API keys");
        }

        public bool IsValidApiKey(string apiKey)
        {
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                _logger.LogWarning("API key validation failed: API key is null or empty");
                return false;
            }

            var isValid = _validApiKeys.Contains(apiKey);
            
            if (!isValid)
            {
                _logger.LogWarning($"API key validation failed: Invalid API key provided");
            }
            else
            {
                _logger.LogDebug("API key validation successful");
            }

            return isValid;
        }

        public bool IsValidApiKeyInRequest(HttpContext context)
        {
            if (context?.Request?.Headers == null)
            {
                _logger.LogWarning("API key validation failed: Invalid HTTP context or headers");
                return false;
            }

            // Try to get the API key from headers
            if (!context.Request.Headers.TryGetValue(_headerName, out var apiKeyValues))
            {
                _logger.LogWarning($"API key validation failed: {_headerName} header not found");
                return false;
            }

            var apiKey = apiKeyValues.FirstOrDefault();
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                _logger.LogWarning($"API key validation failed: {_headerName} header is empty");
                return false;
            }

            return IsValidApiKey(apiKey);
        }

        public string GetApiKeyHeaderName()
        {
            return _headerName;
        }
    }
}
