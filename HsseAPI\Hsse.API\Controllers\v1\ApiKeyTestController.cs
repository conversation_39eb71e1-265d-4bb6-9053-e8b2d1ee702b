using Asp.Versioning;
using Hsse.Application.Helper;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class ApiKeyTestController : ControllerBase
    {
        private readonly IApiKeyTestService _apiKeyTestService;
        private readonly ILogger<ApiKeyTestController> _logger;

        public ApiKeyTestController(
            IApiKeyTestService apiKeyTestService,
            ILogger<ApiKeyTestController> logger)
        {
            _apiKeyTestService = apiKeyTestService;
            _logger = logger;
        }

        /// <summary>
        /// Test endpoint to verify API key validation and encryption
        /// </summary>
        /// <returns>Encrypted response if API key is valid</returns>
        [HttpGet("test")]
        public IActionResult TestApiKeyAndEncryption()
        {
            try
            {
                var response = _apiKeyTestService.PerformApiKeyTest(HttpContext);
                _logger.LogInformation("API Key test endpoint called successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in API key test endpoint");
                return StatusCode(500, "Internal server error during API key test");
            }
        }

        /// <summary>
        /// Test endpoint for encrypted request/response
        /// </summary>
        /// <param name="requestDto">Test data to encrypt/decrypt</param>
        /// <returns>Encrypted response</returns>
        [HttpPost("echo")]
        public IActionResult EchoEncrypted([FromBody] EchoTestRequestDto requestDto)
        {
            try
            {
                var response = _apiKeyTestService.ProcessEchoTest(requestDto, HttpContext);
                _logger.LogInformation("Echo test endpoint called successfully");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in echo test endpoint");
                return StatusCode(500, "Internal server error during echo test");
            }
        }

        /// <summary>
        /// Get API key validation status without encryption
        /// </summary>
        /// <returns>API key validation status</returns>
        [HttpGet("status")]
        public IActionResult GetApiKeyStatus()
        {
            try
            {
                var response = _apiKeyTestService.GetApiKeyStatus(HttpContext);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in API key status endpoint");
                return StatusCode(500, "Internal server error during status check");
            }
        }

        /// <summary>
        /// Get detailed API key validation information
        /// </summary>
        /// <returns>Detailed validation results</returns>
        [HttpGet("validation")]
        public IActionResult GetDetailedValidation()
        {
            try
            {
                var response = _apiKeyTestService.ValidateApiKeyWithDetails(HttpContext);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in detailed validation endpoint");
                return StatusCode(500, "Internal server error during detailed validation");
            }
        }

        /// <summary>
        /// Get API key test statistics
        /// </summary>
        /// <param name="fromDate">Start date for statistics</param>
        /// <param name="toDate">End date for statistics</param>
        /// <returns>Test statistics</returns>
        [HttpGet("statistics")]
        public IActionResult GetTestStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var response = _apiKeyTestService.GetTestStatistics(fromDate, toDate);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in test statistics endpoint");
                return StatusCode(500, "Internal server error during statistics retrieval");
            }
        }
    }
}
