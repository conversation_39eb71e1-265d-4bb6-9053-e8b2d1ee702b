using Asp.Versioning;
using Hsse.Application.Helper;
using Hsse.Application.IServices;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class ApiKeyTestController : ControllerBase
    {
        private readonly EncryptionHelper _encryptionHelper;
        private readonly IApiKeyService _apiKeyService;
        private readonly ILogger<ApiKeyTestController> _logger;

        public ApiKeyTestController(
            EncryptionHelper encryptionHelper, 
            IApiKeyService apiKeyService,
            ILogger<ApiKeyTestController> logger)
        {
            _encryptionHelper = encryptionHelper;
            _apiKeyService = apiKeyService;
            _logger = logger;
        }

        /// <summary>
        /// Test endpoint to verify API key validation and encryption
        /// </summary>
        /// <returns>Encrypted response if API key is valid</returns>
        [HttpGet("test")]
        public IActionResult TestApiKeyAndEncryption()
        {
            var response = new
            {
                message = "API Key validation and encryption working correctly!",
                timestamp = DateTime.UtcNow,
                hasValidApiKey = _apiKeyService.IsValidApiKeyInRequest(HttpContext),
                apiKeyHeader = _apiKeyService.GetApiKeyHeaderName()
            };

            _logger.LogInformation("API Key test endpoint called successfully");
            return Ok(response);
        }

        /// <summary>
        /// Test endpoint for encrypted request/response
        /// </summary>
        /// <param name="testData">Test data to encrypt/decrypt</param>
        /// <returns>Encrypted response</returns>
        [HttpPost("echo")]
        public IActionResult EchoEncrypted([FromBody] object testData)
        {
            var response = new
            {
                message = "Echo test successful",
                receivedData = testData,
                timestamp = DateTime.UtcNow,
                hasValidApiKey = _apiKeyService.IsValidApiKeyInRequest(HttpContext)
            };

            _logger.LogInformation("Echo test endpoint called successfully");
            return Ok(response);
        }

        /// <summary>
        /// Get API key validation status without encryption
        /// </summary>
        /// <returns>API key validation status</returns>
        [HttpGet("status")]
        public IActionResult GetApiKeyStatus()
        {
            var isValid = _apiKeyService.IsValidApiKeyInRequest(HttpContext);
            var headerName = _apiKeyService.GetApiKeyHeaderName();
            
            var response = new
            {
                isValidApiKey = isValid,
                headerName = headerName,
                timestamp = DateTime.UtcNow,
                message = isValid ? "Valid API key provided" : "Invalid or missing API key"
            };

            return Ok(response);
        }
    }
}
