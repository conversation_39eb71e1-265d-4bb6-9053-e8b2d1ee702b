using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Request
{
    public class ApiKeyTestRequestDto
    {
        public string? TestMessage { get; set; }
        public object? TestData { get; set; }
        public DateTime RequestTimestamp { get; set; } = DateTime.UtcNow;
    }

    public class EchoTestRequestDto
    {
        public string? Message { get; set; }
        public object? Data { get; set; }
        public Dictionary<string, object>? AdditionalProperties { get; set; }
    }
}
